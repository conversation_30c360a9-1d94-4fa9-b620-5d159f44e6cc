#!/usr/bin/env python3
"""
Road Checker使用示例

演示如何使用RoadChecker类来检查地图数据的完整性和一致性。
"""

import os
from road_checker import RoadChecker
from road import Road
from crossroad import Crossroad
from node_extractor import NodeExtractor


def example_check_from_file():
    """示例1: 从Webots文件检查地图数据"""
    print("="*60)
    print("示例1: 从Webots文件检查地图数据")
    print("="*60)
    
    # 这里需要替换为实际的.wbt文件路径
    wbt_file = "path/to/your/map.wbt"
    
    if not os.path.exists(wbt_file):
        print(f"警告: 文件 {wbt_file} 不存在，跳过此示例")
        return
    
    checker = RoadChecker()
    result = checker.check_map_data(wbt_file=wbt_file)
    
    # 可以进一步处理结果
    if result['total_errors'] > 0:
        print("发现错误，需要修复！")
    else:
        print("地图数据检查通过！")


def example_check_loaded_data():
    """示例2: 检查已加载的数据"""
    print("="*60)
    print("示例2: 检查已加载的数据")
    print("="*60)
    
    # 假设已经有加载的数据
    if not Road.roads or not Crossroad.crossroads:
        print("没有加载的数据，请先加载道路和十字路口数据")
        return
    
    checker = RoadChecker()
    result = checker.check_map_data()
    
    return result


def example_detailed_error_handling():
    """示例3: 详细的错误处理"""
    print("="*60)
    print("示例3: 详细的错误处理")
    print("="*60)
    
    checker = RoadChecker()
    result = checker.check_map_data()
    
    # 按错误类型分类处理
    error_types = {}
    for error in result['errors']:
        error_type = error['type']
        if error_type not in error_types:
            error_types[error_type] = []
        error_types[error_type].append(error)
    
    # 处理不同类型的错误
    for error_type, errors in error_types.items():
        print(f"\n处理错误类型: {error_type}")
        print(f"数量: {len(errors)}")
        
        if error_type == 'duplicate_road_id':
            print("建议: 为重复的道路ID分配唯一的标识符")
            for error in errors:
                print(f"  - 重复ID: {error['details']['id']}")
        
        elif error_type == 'duplicate_crossroad_id':
            print("建议: 为重复的十字路口ID分配唯一的标识符")
            for error in errors:
                print(f"  - 重复ID: {error['details']['id']}")
        
        elif error_type == 'road_crossroad_id_conflict':
            print("建议: 确保道路和十字路口使用不同的ID命名空间")
            for error in errors:
                print(f"  - 冲突ID: {error['details']['id']}")
        
        elif error_type == 'junction_position_inconsistency':
            print("建议: 检查junction的位置定义，确保相同ID的junction位置一致")
            for error in errors:
                details = error['details']
                print(f"  - Junction ID: {details['junction_id']}")
                print(f"    最大偏差: {details['max_deviation']:.3f}m")
                print(f"    参考位置: {details['reference_position']}")

                # 显示参考道路信息
                ref_road = details['reference_road_info']
                if ref_road['road_name'] != 'N/A':
                    print(f"    参考道路: {ref_road['road_name']} ({ref_road['junction_role']})")

                print(f"    不一致位置数量: {len(details['inconsistent_positions'])}")

                # 显示每个不一致位置的道路信息
                for i, pos_info in enumerate(details['inconsistent_positions'], 1):
                    road_info = pos_info['road_info']
                    if road_info['road_name'] != 'N/A':
                        print(f"      {i}. 道路: {road_info['road_name']} ({road_info['junction_role']}) - 偏差: {pos_info['distance']:.3f}m")


def example_create_test_data():
    """示例4: 创建测试数据并检查"""
    print("="*60)
    print("示例4: 创建测试数据并检查")
    print("="*60)
    
    # 清空现有数据
    Road.roads = []
    Crossroad.crossroads = []
    
    # 创建一些测试道路（包含重复ID的情况）
    test_roads = []
    
    # 正常道路
    road1_wbt = '''Road {
        id "road_001"
        name "Main Street"
        translation 0 0 0
        startJunction "junction_001"
        endJunction "junction_002"
        width 7.0
        numberOfLanes 2
        wayPoints [
            0 0 0
            10 0 0
        ]
    }'''
    
    # 重复ID的道路
    road2_wbt = '''StraightRoadSegment {
        id "road_001"
        name "Side Street"
        translation 0 10 0
        startJunction "junction_003"
        endJunction "junction_004"
        width 7.0
        length 15.0
    }'''
    
    # 正常道路
    road3_wbt = '''Road {
        id "road_002"
        name "Main Street"
        translation 20 0 0
        startJunction "junction_002"
        endJunction "junction_005"
        width 7.0
        numberOfLanes 2
        wayPoints [
            0 0 0
            10 0 0
        ]
    }'''
    
    # 创建Road对象
    road1 = Road(road1_wbt, "Road")
    road2 = Road(road2_wbt, "StraightRoadSegment")
    road3 = Road(road3_wbt, "Road")
    
    Road.roads = [road1, road2, road3]
    
    # 创建一些测试十字路口
    crossroad1_wbt = '''Crossroad {
        id "junction_001"
        name "Main Junction"
        translation 0 0 0
        connectedRoadIDs ["road_001"]
    }'''
    
    crossroad2_wbt = '''Crossroad {
        id "junction_002"
        name "Center Junction"
        translation 10 0 0
        connectedRoadIDs ["road_001", "road_002"]
    }'''
    
    # 位置不一致的junction（相同ID但位置不同）
    crossroad3_wbt = '''RoadIntersection {
        id "junction_002"
        name "Another Center"
        translation 10 5 0
        roadNumber 4
    }'''
    
    crossroad1 = Crossroad("Crossroad")
    crossroad1.init_from_wbt_string(crossroad1_wbt)
    
    crossroad2 = Crossroad("Crossroad")
    crossroad2.init_from_wbt_string(crossroad2_wbt)
    
    crossroad3 = Crossroad("RoadIntersection")
    crossroad3.init_from_wbt_string(crossroad3_wbt)
    
    Crossroad.crossroads = [crossroad1, crossroad2, crossroad3]
    
    # 执行检查
    checker = RoadChecker()
    result = checker.check_map_data()
    
    print(f"\n测试结果:")
    print(f"  - 应该发现重复的道路ID: road_001")
    print(f"  - 应该发现重复的道路名称: Main Street")
    print(f"  - 应该发现重复的十字路口ID: junction_002")
    print(f"  - 应该发现junction位置不一致: junction_002")
    
    return result


def main():
    """主函数"""
    print("Road Checker 使用示例")
    print("="*60)
    
    # 运行示例
    try:
        # 示例1: 从文件检查（如果文件存在）
        example_check_from_file()
        
        # 示例4: 创建测试数据并检查
        example_create_test_data()
        
        # 示例2: 检查已加载的数据
        example_check_loaded_data()
        
        # 示例3: 详细错误处理
        example_detailed_error_handling()
        
    except Exception as e:
        print(f"运行示例时出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
