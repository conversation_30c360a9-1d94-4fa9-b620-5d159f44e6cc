# Road Visualization Tool

这个工具为SUMO导出器添加了道路可视化功能，可以根据从Webots文件中读取的Road坐标、类型、长度和角度信息绘制道路图像。

## 功能特性

- **多种道路类型支持**: 支持Road、StraightRoadSegment、CurvedRoadSegment三种道路类型
- **详细可视化**: 显示道路中心线、宽度区域、路径点、起终点标注
- **道路信息显示**: 可选择显示道路ID、类型、宽度、车道数、限速等信息
- **批量可视化**: 支持同时可视化多个道路对象
- **颜色编码**: 可根据道路类型使用不同颜色
- **灵活输出**: 支持显示和保存图像

## 安装依赖

```bash
pip install matplotlib numpy shapely
```

## 使用方法

### 1. 作为模块使用

```python
from road import Road
from node_extractor import NodeExtractor

# 从Webots文件加载道路
node_extractor = NodeExtractor("your_map.wbt")
Road.roads = []

road_types = ["Road", "StraightRoadSegment", "CurvedRoadSegment"]
for road_type in road_types:
    for road_string in node_extractor.extractRootNodes(road_type):
        road = Road(road_string, road_type)
        Road.roads.append(road)

# 可视化单个道路
road = Road.roads[0]
road.visualize(show=True, save_path="single_road.png")

# 可视化所有道路
Road.visualize_all_roads(show=True, save_path="all_roads.png")
```

### 2. 使用命令行工具

```bash
# 基本用法
python road_visualizer.py path/to/your_map.wbt

# 保存图像
python road_visualizer.py path/to/your_map.wbt --save road_map.png

# 不显示图像，只保存
python road_visualizer.py path/to/your_map.wbt --save road_map.png --no-show

# 显示道路信息
python road_visualizer.py path/to/your_map.wbt --show-info

# 可视化特定道路
python road_visualizer.py path/to/your_map.wbt --road-id "road_1"

# 自定义图像大小
python road_visualizer.py path/to/your_map.wbt --width 20 --height 15

# 设置显示区域范围
python road_visualizer.py path/to/your_map.wbt --x-min -100 --x-max 200 --y-min -50 --y-max 150

# 只限制部分坐标轴
python road_visualizer.py path/to/your_map.wbt --x-min 0 --x-max 100  # 只限制X轴
```

### 3. 运行测试

```bash
# 运行测试脚本，生成示例图像
python test_road_visualization.py
```

## API 参考

### Road.visualize()

可视化单个道路对象。

**参数:**
- `show` (bool): 是否显示图像，默认True
- `save_path` (str): 保存图像的路径，默认None
- `ax` (matplotlib.axes.Axes): 现有坐标轴，默认None
- `color` (str): 道路颜色，默认'blue'
- `alpha` (float): 透明度，默认0.7
- `show_waypoints` (bool): 是否显示路径点，默认True
- `show_info` (bool): 是否显示道路信息，默认True
- `show_name` (bool): 是否显示道路名称，默认True
- `x_min` (float): 显示区域最小X坐标，默认None
- `x_max` (float): 显示区域最大X坐标，默认None
- `y_min` (float): 显示区域最小Y坐标，默认None
- `y_max` (float): 显示区域最大Y坐标，默认None

### Road.visualize_all_roads()

可视化多个道路对象。

**参数:**
- `roads` (list): Road对象列表，默认使用Road.roads
- `show` (bool): 是否显示图像，默认True
- `save_path` (str): 保存图像的路径，默认None
- `figsize` (tuple): 图像大小，默认(15, 10)
- `show_waypoints` (bool): 是否显示路径点，默认True
- `show_info` (bool): 是否显示道路信息，默认False
- `color_by_type` (bool): 是否根据类型着色，默认True
- `show_names` (bool): 是否显示道路名称，默认True
- `x_min` (float): 显示区域最小X坐标，默认None
- `x_max` (float): 显示区域最大X坐标，默认None
- `y_min` (float): 显示区域最小Y坐标，默认None
- `y_max` (float): 显示区域最大Y坐标，默认None

### Road.get_road_info()

获取道路的详细信息。

**返回:**
- `dict`: 包含道路所有信息的字典

## 可视化效果

### 道路类型颜色编码
- **Road**: 蓝色 - 复杂道路（带路径点）
- **StraightRoadSegment**: 绿色 - 直线道路段
- **CurvedRoadSegment**: 橙色 - 弯曲道路段

### 显示元素
- **道路宽度**: 半透明区域显示道路实际宽度（底层显示）
- **道路中心线**: 粗线显示道路走向（中层显示）
- **路径点**: 红色圆点标记关键路径点（底层显示，避免遮挡）
- **起终点**: 与道路线段同色的"Start"和"End"标注
  - Start: 从起点向线段内部偏移显示（朝向第二个点的方向）
    - 格式: "Start [StartJunctionID]" 或 "Start"（如果没有junction信息）
  - End: 从终点向线段内部偏移显示（朝向倒数第二个点的方向）
    - 格式: "End [EndJunctionID]" 或 "End"（如果没有junction信息）
  - 带白色背景框，避免与其他道路标注重合
- **道路名称**: 白色背景文本框显示在道路几何中心（顶层显示）
- **道路信息**: 黄色背景小字体文本框显示详细信息，包括：
  - ID: 道路标识符
  - Lanes: 总车道数
  - Forward Lanes: 前向车道数
  - Start Junction: 起始交叉口ID（如果有）
  - End Junction: 结束交叉口ID（如果有）

### 显示层次（zorder）
1. **底层 (zorder=1)**: 路径点
2. **底层 (zorder=2)**: 道路宽度区域
3. **中层 (zorder=3)**: 道路中心线
4. **高层 (zorder=10)**: Start/End标注
5. **信息层 (zorder=12)**: 道路详细信息
6. **顶层 (zorder=15)**: 道路名称

## 示例输出

运行测试脚本后会生成以下示例图像：
- `test_straight_road.png`: 直线道路可视化
- `test_curved_road.png`: 弯曲道路可视化
- `test_complex_road.png`: 复杂道路可视化
- `test_all_roads.png`: 所有道路综合可视化

## 故障排除

### 常见问题

1. **ImportError: No module named 'matplotlib'**
   - 解决方案: `pip install matplotlib`

2. **道路宽度显示异常**
   - 可能原因: shapely库计算失败
   - 自动回退: 系统会自动使用简化的矩形绘制方法

3. **图像显示空白**
   - 检查道路是否有有效的wayPoints
   - 确认道路坐标范围是否合理

4. **内存使用过多**
   - 对于大量道路，建议设置`show=False`并保存图像
   - 可以分批可视化道路

### 性能优化建议

- 对于大型地图，建议关闭`show_info`以提高性能
- 使用`show=False`并保存图像可以避免GUI开销
- 可以通过`--road-id`参数单独查看特定道路

## 扩展功能

可以通过继承Road类或修改可视化方法来添加更多功能：
- 添加交通标志可视化
- 显示车道分隔线
- 集成交叉路口可视化
- 添加3D可视化支持
