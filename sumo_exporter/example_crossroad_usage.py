#!/usr/bin/env python3
"""
Example usage of crossroad visualization functionality.

This script demonstrates different ways to visualize crossroads from Webots files.
"""

import os
import sys

# Add the current directory to Python path to import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from road import Road
from crossroad import Crossroad
from node_extractor import NodeExtractor
from road_visualizer import (
    visualize_crossroads_from_file,
    visualize_roads_and_crossroads_from_file
)


def example_1_basic_crossroad_visualization():
    """Example 1: Basic crossroad visualization from a WBT file."""
    print("Example 1: Basic crossroad visualization")
    print("-" * 40)
    
    # Replace with your actual WBT file path
    wbt_file = "../webots_simulation/worlds/04raozhang/raozhang.wbt"
    
    if not os.path.exists(wbt_file):
        print(f"WBT file not found: {wbt_file}")
        print("Please update the file path in the script.")
        return
    
    # Visualize only crossroads
    crossroads = visualize_crossroads_from_file(
        wbt_file,
        save_path="example_crossroads.png",
        show=True,
        show_names=True,
        show_connected_roads=True,
        color_by_type=True
    )
    
    print(f"Visualized {len(crossroads)} crossroads")
    print()


def example_2_combined_visualization():
    """Example 2: Combined roads and crossroads visualization."""
    print("Example 2: Combined roads and crossroads visualization")
    print("-" * 50)
    
    # Replace with your actual WBT file path
    wbt_file = "../webots_simulation/worlds/04raozhang/raozhang.wbt"
    
    if not os.path.exists(wbt_file):
        print(f"WBT file not found: {wbt_file}")
        print("Please update the file path in the script.")
        return
    
    # Visualize both roads and crossroads
    roads, crossroads = visualize_roads_and_crossroads_from_file(
        wbt_file,
        save_path="example_combined.png",
        show=True,
        show_waypoints=True,
        show_names=True,
        show_crossroads=True,
        show_connected_roads=True,
        color_by_type=True
    )
    
    print(f"Visualized {len(roads)} roads and {len(crossroads)} crossroads")
    print()


def example_3_programmatic_usage():
    """Example 3: Programmatic usage with direct object manipulation."""
    print("Example 3: Programmatic usage")
    print("-" * 30)
    
    # Replace with your actual WBT file path
    wbt_file = "../webots_simulation/worlds/04raozhang/raozhang.wbt"
    
    if not os.path.exists(wbt_file):
        print(f"WBT file not found: {wbt_file}")
        print("Please update the file path in the script.")
        return
    
    # Extract crossroads manually
    node_extractor = NodeExtractor(wbt_file)
    Crossroad.crossroads = []
    
    crossroad_types = ["Crossroad", "RoadIntersection", "LaneSeparation"]
    for crossroad_type in crossroad_types:
        for crossroad_string in node_extractor.extractRootNodes(crossroad_type):
            crossroad = Crossroad(crossroad_type)
            crossroad.init_from_wbt_string(crossroad_string)
            Crossroad.crossroads.append(crossroad)
    
    print(f"Found {len(Crossroad.crossroads)} crossroads:")
    for crossroad in Crossroad.crossroads:
        print(f"  - ID: {crossroad.id}")
        print(f"    Name: {crossroad.name or 'No name'}")
        print(f"    Type: {crossroad.crossroadType}")
        print(f"    Position: ({crossroad.translation[0]:.1f}, {crossroad.translation[1]:.1f})")
        print(f"    Connected Roads: {', '.join(crossroad.connectedRoadIDs) if crossroad.connectedRoadIDs else 'None'}")
        print()
    
    # Visualize all crossroads
    if Crossroad.crossroads:
        Crossroad.visualize_all_crossroads(
            save_path="example_programmatic.png",
            show=True,
            show_names=True,
            show_connected_roads=True
        )
    
    print()


def example_4_single_crossroad():
    """Example 4: Visualize a single crossroad."""
    print("Example 4: Single crossroad visualization")
    print("-" * 40)
    
    # Replace with your actual WBT file path
    wbt_file = "../webots_simulation/worlds/04raozhang/raozhang.wbt"
    
    if not os.path.exists(wbt_file):
        print(f"WBT file not found: {wbt_file}")
        print("Please update the file path in the script.")
        return
    
    # Extract crossroads
    node_extractor = NodeExtractor(wbt_file)
    Crossroad.crossroads = []
    
    crossroad_types = ["Crossroad", "RoadIntersection", "LaneSeparation"]
    for crossroad_type in crossroad_types:
        for crossroad_string in node_extractor.extractRootNodes(crossroad_type):
            crossroad = Crossroad(crossroad_type)
            crossroad.init_from_wbt_string(crossroad_string)
            Crossroad.crossroads.append(crossroad)
    
    # Visualize the first crossroad individually
    if Crossroad.crossroads:
        first_crossroad = Crossroad.crossroads[0]
        print(f"Visualizing crossroad: {first_crossroad.id}")
        
        first_crossroad.visualize(
            save_path="example_single_crossroad.png",
            show=True,
            show_name=True,
            show_connected_roads=True
        )
    else:
        print("No crossroads found in the file.")
    
    print()


def main():
    """Main function to run all examples."""
    print("Crossroad Visualization Examples")
    print("=" * 40)
    print()
    
    # Run examples
    try:
        example_1_basic_crossroad_visualization()
        example_2_combined_visualization()
        example_3_programmatic_usage()
        example_4_single_crossroad()
        
        print("All examples completed!")
        print("\nGenerated files:")
        for filename in ["example_crossroads.png", "example_combined.png", 
                        "example_programmatic.png", "example_single_crossroad.png"]:
            if os.path.exists(filename):
                print(f"  - {filename}")
                
    except Exception as e:
        print(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
