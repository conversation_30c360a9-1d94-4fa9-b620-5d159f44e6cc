#!/usr/bin/env python3
"""
Test script for crossroad visualization functionality.

This script demonstrates how to use the crossroad visualization features.
"""

import os
import sys

# Add the current directory to Python path to import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from road_visualizer import (
    visualize_crossroads_from_file,
    visualize_roads_and_crossroads_from_file
)


def test_crossroad_visualization():
    """Test crossroad visualization with a sample WBT file."""
    
    # Look for WBT files in common locations
    test_files = [
        "../webots_simulation/worlds/04raozhang/raozhang.wbt",
        "test_files/sample.wbt",
        "sample.wbt"
    ]
    
    wbt_file = None
    for file_path in test_files:
        if os.path.exists(file_path):
            wbt_file = file_path
            break
    
    if not wbt_file:
        print("No test WBT file found. Please provide a WBT file path.")
        print("Looking for files in:")
        for file_path in test_files:
            print(f"  - {file_path}")
        return
    
    print(f"Testing crossroad visualization with: {wbt_file}")
    print("=" * 60)
    
    # Test 1: Visualize only crossroads
    print("\n1. Testing crossroads-only visualization...")
    try:
        crossroads = visualize_crossroads_from_file(
            wbt_file,
            save_path="test_crossroads_only.png",
            show=False,  # Don't show to avoid blocking in automated tests
            show_names=True,
            show_connected_roads=True,
            color_by_type=True
        )
        print(f"   ✓ Successfully visualized {len(crossroads)} crossroads")
        if os.path.exists("test_crossroads_only.png"):
            print("   ✓ Image saved as test_crossroads_only.png")
    except Exception as e:
        print(f"   ✗ Error: {e}")
    
    # Test 2: Visualize roads and crossroads together
    print("\n2. Testing combined roads and crossroads visualization...")
    try:
        roads, crossroads = visualize_roads_and_crossroads_from_file(
            wbt_file,
            save_path="test_roads_and_crossroads.png",
            show=False,  # Don't show to avoid blocking in automated tests
            show_waypoints=True,
            show_names=True,
            show_crossroads=True,
            show_connected_roads=True,
            color_by_type=True
        )
        print(f"   ✓ Successfully visualized {len(roads)} roads and {len(crossroads)} crossroads")
        if os.path.exists("test_roads_and_crossroads.png"):
            print("   ✓ Image saved as test_roads_and_crossroads.png")
    except Exception as e:
        print(f"   ✗ Error: {e}")
    
    print("\n" + "=" * 60)
    print("Test completed!")
    print("\nGenerated files:")
    for filename in ["test_crossroads_only.png", "test_roads_and_crossroads.png"]:
        if os.path.exists(filename):
            print(f"  - {filename}")


def main():
    """Main function for testing."""
    if len(sys.argv) > 1:
        # If a WBT file is provided as argument
        wbt_file = sys.argv[1]
        if not os.path.exists(wbt_file):
            print(f"Error: File {wbt_file} does not exist.")
            sys.exit(1)
        
        print(f"Testing with provided file: {wbt_file}")
        
        # Test crossroads only
        print("\nVisualizing crossroads only...")
        crossroads = visualize_crossroads_from_file(
            wbt_file,
            save_path="crossroads_visualization.png",
            show=True,
            show_names=True,
            show_connected_roads=True
        )
        
        # Test combined visualization
        print("\nVisualizing roads and crossroads together...")
        roads, crossroads = visualize_roads_and_crossroads_from_file(
            wbt_file,
            save_path="combined_visualization.png",
            show=True,
            show_names=True,
            show_crossroads=True,
            show_connected_roads=True
        )
        
    else:
        # Run automated tests
        test_crossroad_visualization()


if __name__ == "__main__":
    main()
