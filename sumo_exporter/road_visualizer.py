#!/usr/bin/env python3
"""
Road Visualization Tool

This script provides functionality to visualize Road objects from Webots files.
It can be used as a standalone script or imported as a module.

Usage:
    python road_visualizer.py <webots_file.wbt> [options]
    
Example:
    python road_visualizer.py ../simulation_file/maps/example.wbt --save road_map.png
"""

import argparse
import sys
import os
from pathlib import Path

# Add the current directory to Python path to import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from road import Road
from crossroad import Crossroad
from node_extractor import NodeExtractor


def visualize_roads_from_file(wbt_file, save_path=None, show=True,
                             show_waypoints=True, show_info=False,
                             color_by_type=True, show_names=True, figsize=(15, 10)):
    """
    从Webots文件中提取并可视化所有道路

    Args:
        wbt_file (str): Webots文件路径
        save_path (str): 保存图像的路径
        show (bool): 是否显示图像
        show_waypoints (bool): 是否显示路径点
        show_info (bool): 是否显示道路信息
        color_by_type (bool): 是否根据道路类型使用不同颜色
        show_names (bool): 是否在道路中央显示名称
        figsize (tuple): 图像大小

    Returns:
        list: Road对象列表
    """
    if not os.path.exists(wbt_file):
        print(f"Error: File {wbt_file} does not exist.")
        return []
        
    print(f"Loading roads from {wbt_file}...")
    
    # 清空现有的roads列表
    Road.roads = []
    
    # 提取道路信息
    node_extractor = NodeExtractor(wbt_file)
    road_types = ["Road", "StraightRoadSegment", "CurvedRoadSegment"]
    
    for road_type in road_types:
        for road_string in node_extractor.extractRootNodes(road_type):
            road = Road(road_string, road_type)
            Road.roads.append(road)
    
    print(f"Found {len(Road.roads)} roads:")
    for road in Road.roads:
        info = road.get_road_info()
        print(f"  - {info['id']}: {info['type']}, {info['waypoints_count']} waypoints, "
              f"{info['road_length']:.1f}m length")
    
    if not Road.roads:
        print("No roads found in the file.")
        return []
    
    # 可视化所有道路
    print("Visualizing roads...")
    Road.visualize_all_roads(
        show=show,
        save_path=save_path,
        figsize=figsize,
        show_waypoints=show_waypoints,
        show_info=show_info,
        color_by_type=color_by_type,
        show_names=show_names
    )
    
    return Road.roads


def visualize_roads_and_crossroads_from_file(wbt_file, save_path=None, show=True,
                                           show_waypoints=True, show_info=False,
                                           color_by_type=True, show_names=True,
                                           show_crossroads=True, show_connected_roads=True,
                                           figsize=(15, 10)):
    """
    从Webots文件中提取并可视化所有道路和十字路口

    Args:
        wbt_file (str): Webots文件路径
        save_path (str): 保存图像的路径
        show (bool): 是否显示图像
        show_waypoints (bool): 是否显示路径点
        show_info (bool): 是否显示道路信息
        color_by_type (bool): 是否根据道路类型使用不同颜色
        show_names (bool): 是否在道路中央显示名称
        show_crossroads (bool): 是否显示十字路口
        show_connected_roads (bool): 是否显示十字路口连接的道路ID
        figsize (tuple): 图像大小

    Returns:
        tuple: (Road对象列表, Crossroad对象列表)
    """
    if not os.path.exists(wbt_file):
        print(f"Error: File {wbt_file} does not exist.")
        return [], []

    print(f"Loading roads and crossroads from {wbt_file}...")

    # 清空现有的roads和crossroads列表
    Road.roads = []
    Crossroad.crossroads = []

    # 提取道路信息
    node_extractor = NodeExtractor(wbt_file)
    road_types = ["Road", "StraightRoadSegment", "CurvedRoadSegment"]

    for road_type in road_types:
        for road_string in node_extractor.extractRootNodes(road_type):
            road = Road(road_string, road_type)
            Road.roads.append(road)

    # 提取十字路口信息
    crossroad_types = ["Crossroad", "RoadIntersection", "LaneSeparation"]
    for crossroad_type in crossroad_types:
        for crossroad_string in node_extractor.extractRootNodes(crossroad_type):
            crossroad = Crossroad(crossroad_type)
            crossroad.init_from_wbt_string(crossroad_string)
            Crossroad.crossroads.append(crossroad)

    print(f"Found {len(Road.roads)} roads:")
    for road in Road.roads:
        info = road.get_road_info()
        print(f"  - {info['id']}: {info['type']}, {info['waypoints_count']} waypoints, "
              f"{info['road_length']:.1f}m length")

    print(f"Found {len(Crossroad.crossroads)} crossroads:")
    for crossroad in Crossroad.crossroads:
        connected_roads_str = ", ".join(crossroad.connectedRoadIDs) if crossroad.connectedRoadIDs else "None"
        print(f"  - {crossroad.id} ({crossroad.name or 'No name'}): {crossroad.crossroadType}, "
              f"connected to roads: {connected_roads_str}")

    if not Road.roads and not Crossroad.crossroads:
        print("No roads or crossroads found in the file.")
        return [], []

    # 创建组合可视化
    print("Visualizing roads and crossroads...")

    try:
        import matplotlib.pyplot as plt

        # 创建图形
        fig, ax = plt.subplots(figsize=figsize)

        # 定义道路类型颜色
        road_type_colors = {
            'Road': 'blue',
            'StraightRoadSegment': 'green',
            'CurvedRoadSegment': 'orange'
        }

        # 绘制所有道路
        for i, road in enumerate(Road.roads):
            if len(road.wayPoints) < 2:
                continue

            # 选择道路颜色
            if color_by_type and road.roadType in road_type_colors:
                color = road_type_colors[road.roadType]
            else:
                # 使用循环颜色
                colors = ['blue', 'green', 'red', 'orange', 'purple', 'brown', 'pink', 'gray']
                color = colors[i % len(colors)]

            # 绘制道路
            road.visualize(show=False, ax=ax, color=color,
                          show_waypoints=show_waypoints, show_info=show_info, show_name=show_names)

        # 绘制所有十字路口
        if show_crossroads:
            crossroad_type_colors = {
                'Crossroad': 'red',
                'RoadIntersection': 'orange',
                'LaneSeparation': 'purple'
            }

            for i, crossroad in enumerate(Crossroad.crossroads):
                # 选择十字路口颜色
                if color_by_type and crossroad.crossroadType in crossroad_type_colors:
                    color = crossroad_type_colors[crossroad.crossroadType]
                else:
                    # 使用循环颜色
                    colors = ['red', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
                    color = colors[i % len(colors)]

                # 绘制十字路口
                crossroad.visualize(show=False, ax=ax, color=color,
                                  show_name=show_names,
                                  show_connected_roads=show_connected_roads)

        # 设置图形属性
        ax.set_title('Roads and Crossroads Visualization')
        ax.set_xlabel('X Coordinate (m)')
        ax.set_ylabel('Y Coordinate (m)')
        ax.grid(True, alpha=0.3)
        ax.axis('equal')

        # 添加统计信息
        stats_text = f"Total Roads: {len(Road.roads)}\n"
        road_type_counts = {}
        for road in Road.roads:
            road_type_counts[road.roadType] = road_type_counts.get(road.roadType, 0) + 1

        for road_type, count in road_type_counts.items():
            stats_text += f"{road_type}: {count}\n"

        if show_crossroads:
            stats_text += f"\nTotal Crossroads: {len(Crossroad.crossroads)}\n"
            crossroad_type_counts = {}
            for crossroad in Crossroad.crossroads:
                crossroad_type_counts[crossroad.crossroadType] = crossroad_type_counts.get(crossroad.crossroadType, 0) + 1

            for crossroad_type, count in crossroad_type_counts.items():
                stats_text += f"{crossroad_type}: {count}\n"

        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
               verticalalignment='top', bbox=dict(boxstyle="round,pad=0.3",
               facecolor='white', alpha=0.8))

        # 保存图像
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        # 显示图像
        if show:
            plt.show()
        else:
            plt.close()

    except ImportError:
        print("Error: matplotlib not available. Cannot create visualization.")

    return Road.roads, Crossroad.crossroads


def visualize_crossroads_from_file(wbt_file, save_path=None, show=True,
                                  show_names=True, show_connected_roads=True,
                                  color_by_type=True, figsize=(15, 10)):
    """
    从Webots文件中提取并可视化所有十字路口

    Args:
        wbt_file (str): Webots文件路径
        save_path (str): 保存图像的路径
        show (bool): 是否显示图像
        show_names (bool): 是否显示十字路口名称
        show_connected_roads (bool): 是否显示连接的道路ID
        color_by_type (bool): 是否根据十字路口类型使用不同颜色
        figsize (tuple): 图像大小

    Returns:
        list: Crossroad对象列表
    """
    if not os.path.exists(wbt_file):
        print(f"Error: File {wbt_file} does not exist.")
        return []

    print(f"Loading crossroads from {wbt_file}...")

    # 清空现有的crossroads列表
    Crossroad.crossroads = []

    # 提取十字路口信息
    node_extractor = NodeExtractor(wbt_file)
    crossroad_types = ["Crossroad", "RoadIntersection", "LaneSeparation"]

    for crossroad_type in crossroad_types:
        for crossroad_string in node_extractor.extractRootNodes(crossroad_type):
            crossroad = Crossroad(crossroad_type)
            crossroad.init_from_wbt_string(crossroad_string)
            Crossroad.crossroads.append(crossroad)

    print(f"Found {len(Crossroad.crossroads)} crossroads:")
    for crossroad in Crossroad.crossroads:
        connected_roads_str = ", ".join(crossroad.connectedRoadIDs) if crossroad.connectedRoadIDs else "None"
        print(f"  - {crossroad.id} ({crossroad.name or 'No name'}): {crossroad.crossroadType}, "
              f"connected to roads: {connected_roads_str}")

    if not Crossroad.crossroads:
        print("No crossroads found in the file.")
        return []

    # 可视化所有十字路口
    print("Visualizing crossroads...")
    Crossroad.visualize_all_crossroads(
        show=show,
        save_path=save_path,
        figsize=figsize,
        show_names=show_names,
        show_connected_roads=show_connected_roads,
        color_by_type=color_by_type
    )

    return Crossroad.crossroads


def visualize_single_road(road, save_path=None, show=True):
    """
    可视化单个道路
    
    Args:
        road (Road): Road对象
        save_path (str): 保存图像的路径
        show (bool): 是否显示图像
    """
    print(f"Visualizing road: {road.id}")
    info = road.get_road_info()
    
    print("Road Information:")
    for key, value in info.items():
        if value is not None:
            print(f"  {key}: {value}")
    
    road.visualize(show=show, save_path=save_path, show_info=True)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Visualize roads and crossroads from Webots files')
    parser.add_argument('wbt_file', help='Path to the Webots (.wbt) file')
    parser.add_argument('--save', '-s', help='Save visualization to file')
    parser.add_argument('--no-show', action='store_true', help='Do not display the visualization')
    parser.add_argument('--no-waypoints', action='store_true', help='Do not show waypoints')
    parser.add_argument('--show-info', action='store_true', help='Show road information (ID, lanes, junctions) on the plot')
    parser.add_argument('--no-color-by-type', action='store_true', help='Do not color roads by type')
    parser.add_argument('--no-names', action='store_true', help='Do not show road names in the center')
    parser.add_argument('--width', type=int, default=15, help='Figure width (default: 15)')
    parser.add_argument('--height', type=int, default=10, help='Figure height (default: 10)')
    parser.add_argument('--road-id', help='Visualize only a specific road by ID')

    # 十字路口相关选项
    parser.add_argument('--crossroads-only', action='store_true', help='Visualize only crossroads')
    parser.add_argument('--no-crossroads', action='store_true', help='Do not show crossroads')
    parser.add_argument('--no-connected-roads', action='store_true', help='Do not show connected road IDs for crossroads')
    parser.add_argument('--roads-and-crossroads', action='store_true', help='Visualize both roads and crossroads together')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.wbt_file):
        print(f"Error: File {args.wbt_file} does not exist.")
        sys.exit(1)
    
    try:
        # 根据选项决定可视化内容
        if args.crossroads_only:
            # 只可视化十字路口
            crossroads = visualize_crossroads_from_file(
                args.wbt_file,
                save_path=args.save,
                show=not args.no_show,
                show_names=not args.no_names,
                show_connected_roads=not args.no_connected_roads,
                color_by_type=not args.no_color_by_type,
                figsize=(args.width, args.height)
            )
            roads = []
        elif args.roads_and_crossroads:
            # 可视化道路和十字路口
            roads, crossroads = visualize_roads_and_crossroads_from_file(
                args.wbt_file,
                save_path=args.save,
                show=not args.no_show,
                show_waypoints=not args.no_waypoints,
                show_info=args.show_info,
                color_by_type=not args.no_color_by_type,
                show_names=not args.no_names,
                show_crossroads=not args.no_crossroads,
                show_connected_roads=not args.no_connected_roads,
                figsize=(args.width, args.height)
            )
        else:
            # 默认只可视化道路
            roads = visualize_roads_from_file(
                args.wbt_file,
                save_path=args.save,
                show=not args.no_show,
                show_waypoints=not args.no_waypoints,
                show_info=args.show_info,
                color_by_type=not args.no_color_by_type,
                show_names=not args.no_names,
                figsize=(args.width, args.height)
            )
            crossroads = []
        
        # 如果指定了特定道路ID，单独可视化该道路
        if args.road_id:
            target_road = None
            for road in roads:
                if road.id == args.road_id:
                    target_road = road
                    break
            
            if target_road:
                save_path = None
                if args.save:
                    # 为单个道路创建不同的保存路径
                    base_path = Path(args.save)
                    save_path = str(base_path.parent / f"{base_path.stem}_{args.road_id}{base_path.suffix}")
                
                visualize_single_road(target_road, save_path, not args.no_show)
            else:
                print(f"Error: Road with ID '{args.road_id}' not found.")
                print("Available road IDs:")
                for road in roads:
                    print(f"  - {road.id}")
                sys.exit(1)
        
        print("Visualization completed successfully!")
        
    except Exception as e:
        print(f"Error during visualization: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
