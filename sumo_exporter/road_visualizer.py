#!/usr/bin/env python3
"""
Road Visualization Tool

This script provides functionality to visualize Road objects from Webots files.
It can be used as a standalone script or imported as a module.

Usage:
    python road_visualizer.py <webots_file.wbt> [options]
    
Example:
    python road_visualizer.py ../simulation_file/maps/example.wbt --save road_map.png
"""

import argparse
import sys
import os
from pathlib import Path

# Add the current directory to Python path to import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from road import Road
from node_extractor import NodeExtractor


def visualize_roads_from_file(wbt_file, save_path=None, show=True,
                             show_waypoints=True, show_info=False,
                             color_by_type=True, show_names=True, figsize=(15, 10)):
    """
    从Webots文件中提取并可视化所有道路

    Args:
        wbt_file (str): Webots文件路径
        save_path (str): 保存图像的路径
        show (bool): 是否显示图像
        show_waypoints (bool): 是否显示路径点
        show_info (bool): 是否显示道路信息
        color_by_type (bool): 是否根据道路类型使用不同颜色
        show_names (bool): 是否在道路中央显示名称
        figsize (tuple): 图像大小

    Returns:
        list: Road对象列表
    """
    if not os.path.exists(wbt_file):
        print(f"Error: File {wbt_file} does not exist.")
        return []
        
    print(f"Loading roads from {wbt_file}...")
    
    # 清空现有的roads列表
    Road.roads = []
    
    # 提取道路信息
    node_extractor = NodeExtractor(wbt_file)
    road_types = ["Road", "StraightRoadSegment", "CurvedRoadSegment"]
    
    for road_type in road_types:
        for road_string in node_extractor.extractRootNodes(road_type):
            road = Road(road_string, road_type)
            Road.roads.append(road)
    
    print(f"Found {len(Road.roads)} roads:")
    for road in Road.roads:
        info = road.get_road_info()
        print(f"  - {info['id']}: {info['type']}, {info['waypoints_count']} waypoints, "
              f"{info['road_length']:.1f}m length")
    
    if not Road.roads:
        print("No roads found in the file.")
        return []
    
    # 可视化所有道路
    print("Visualizing roads...")
    Road.visualize_all_roads(
        show=show,
        save_path=save_path,
        figsize=figsize,
        show_waypoints=show_waypoints,
        show_info=show_info,
        color_by_type=color_by_type,
        show_names=show_names
    )
    
    return Road.roads


def visualize_single_road(road, save_path=None, show=True):
    """
    可视化单个道路
    
    Args:
        road (Road): Road对象
        save_path (str): 保存图像的路径
        show (bool): 是否显示图像
    """
    print(f"Visualizing road: {road.id}")
    info = road.get_road_info()
    
    print("Road Information:")
    for key, value in info.items():
        if value is not None:
            print(f"  {key}: {value}")
    
    road.visualize(show=show, save_path=save_path, show_info=True)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Visualize roads from Webots files')
    parser.add_argument('wbt_file', help='Path to the Webots (.wbt) file')
    parser.add_argument('--save', '-s', help='Save visualization to file')
    parser.add_argument('--no-show', action='store_true', help='Do not display the visualization')
    parser.add_argument('--no-waypoints', action='store_true', help='Do not show waypoints')
    parser.add_argument('--show-info', action='store_true', help='Show road information (ID, lanes, junctions) on the plot')
    parser.add_argument('--no-color-by-type', action='store_true', help='Do not color roads by type')
    parser.add_argument('--no-names', action='store_true', help='Do not show road names in the center')
    parser.add_argument('--width', type=int, default=15, help='Figure width (default: 15)')
    parser.add_argument('--height', type=int, default=10, help='Figure height (default: 10)')
    parser.add_argument('--road-id', help='Visualize only a specific road by ID')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.wbt_file):
        print(f"Error: File {args.wbt_file} does not exist.")
        sys.exit(1)
    
    try:
        # 加载道路
        roads = visualize_roads_from_file(
            args.wbt_file,
            save_path=args.save,
            show=not args.no_show,
            show_waypoints=not args.no_waypoints,
            show_info=args.show_info,
            color_by_type=not args.no_color_by_type,
            show_names=not args.no_names,
            figsize=(args.width, args.height)
        )
        
        # 如果指定了特定道路ID，单独可视化该道路
        if args.road_id:
            target_road = None
            for road in roads:
                if road.id == args.road_id:
                    target_road = road
                    break
            
            if target_road:
                save_path = None
                if args.save:
                    # 为单个道路创建不同的保存路径
                    base_path = Path(args.save)
                    save_path = str(base_path.parent / f"{base_path.stem}_{args.road_id}{base_path.suffix}")
                
                visualize_single_road(target_road, save_path, not args.no_show)
            else:
                print(f"Error: Road with ID '{args.road_id}' not found.")
                print("Available road IDs:")
                for road in roads:
                    print(f"  - {road.id}")
                sys.exit(1)
        
        print("Visualization completed successfully!")
        
    except Exception as e:
        print(f"Error during visualization: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
