#!/usr/bin/env python3
"""
Simple test for crossroad visualization functionality.
"""

import os
import sys

# Add the current directory to Python path to import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_crossroad_import():
    """Test if we can import and create a Crossroad object."""
    try:
        from crossroad import Crossroad
        print("✓ Successfully imported Crossroad class")
        
        # Create a simple crossroad
        crossroad = Crossroad("Crossroad")
        print("✓ Successfully created Crossroad object")
        
        # Set some basic properties
        crossroad.id = "test_crossroad"
        crossroad.name = "Test Crossroad"
        crossroad.translation = [10.0, 20.0, 0.0]
        crossroad.connectedRoadIDs = ["road_1", "road_2", "road_3"]
        
        print(f"✓ Crossroad ID: {crossroad.id}")
        print(f"✓ Crossroad Name: {crossroad.name}")
        print(f"✓ Crossroad Position: {crossroad.translation}")
        print(f"✓ Connected Roads: {crossroad.connectedRoadIDs}")
        
        return crossroad
        
    except Exception as e:
        print(f"✗ Error importing or creating Crossroad: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_crossroad_visualization(crossroad):
    """Test crossroad visualization."""
    if not crossroad:
        print("✗ No crossroad to visualize")
        return
        
    try:
        print("\nTesting crossroad visualization...")
        
        # Test single crossroad visualization
        crossroad.visualize(
            show=False,  # Don't show to avoid blocking
            save_path="test_single_crossroad.png",
            show_name=True,
            show_connected_roads=True
        )
        
        if os.path.exists("test_single_crossroad.png"):
            print("✓ Successfully created single crossroad visualization")
            print("  - Saved as: test_single_crossroad.png")
        else:
            print("✗ Failed to save single crossroad visualization")
            
    except Exception as e:
        print(f"✗ Error in crossroad visualization: {e}")
        import traceback
        traceback.print_exc()

def test_multiple_crossroads():
    """Test multiple crossroads visualization."""
    try:
        from crossroad import Crossroad
        
        print("\nTesting multiple crossroads visualization...")
        
        # Clear existing crossroads
        Crossroad.crossroads = []
        
        # Create multiple test crossroads
        for i in range(3):
            crossroad = Crossroad("Crossroad")
            crossroad.id = f"crossroad_{i+1}"
            crossroad.name = f"Test Crossroad {i+1}"
            crossroad.translation = [i * 30.0, i * 20.0, 0.0]
            crossroad.connectedRoadIDs = [f"road_{i+1}_1", f"road_{i+1}_2"]
            Crossroad.crossroads.append(crossroad)
        
        print(f"✓ Created {len(Crossroad.crossroads)} test crossroads")
        
        # Visualize all crossroads
        Crossroad.visualize_all_crossroads(
            show=False,  # Don't show to avoid blocking
            save_path="test_multiple_crossroads.png",
            show_names=True,
            show_connected_roads=True
        )
        
        if os.path.exists("test_multiple_crossroads.png"):
            print("✓ Successfully created multiple crossroads visualization")
            print("  - Saved as: test_multiple_crossroads.png")
        else:
            print("✗ Failed to save multiple crossroads visualization")
            
    except Exception as e:
        print(f"✗ Error in multiple crossroads visualization: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main test function."""
    print("Crossroad Visualization Test")
    print("=" * 40)
    
    # Test 1: Import and create crossroad
    crossroad = test_crossroad_import()
    
    # Test 2: Single crossroad visualization
    test_crossroad_visualization(crossroad)
    
    # Test 3: Multiple crossroads visualization
    test_multiple_crossroads()
    
    print("\n" + "=" * 40)
    print("Test completed!")
    
    # List generated files
    print("\nGenerated files:")
    for filename in ["test_single_crossroad.png", "test_multiple_crossroads.png"]:
        if os.path.exists(filename):
            print(f"  ✓ {filename}")
        else:
            print(f"  ✗ {filename} (not created)")

if __name__ == "__main__":
    main()
