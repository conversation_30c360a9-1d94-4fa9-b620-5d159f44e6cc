#!/usr/bin/env python3
"""
简单测试脚本，验证RoadChecker的功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from road_checker import RoadChecker
    from road import Road
    from crossroad import Crossroad
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在sumo_exporter目录下运行此脚本")
    sys.exit(1)


def create_test_data_with_position_inconsistency():
    """创建包含位置不一致问题的测试数据"""
    print("创建测试数据...")
    
    # 清空现有数据
    Road.roads = []
    Crossroad.crossroads = []
    
    # 创建测试道路
    # 道路1: 从junction_001到junction_002
    road1_wbt = '''Road {
        id "main_road"
        name "Main Street"
        translation 0 0 0
        startJunction "junction_001"
        endJunction "junction_002"
        width 7.0
        numberOfLanes 2
        wayPoints [
            0 0 0
            20 0 0
        ]
    }'''
    
    # 道路2: 从junction_002到junction_003 (与道路1共享junction_002)
    road2_wbt = '''Road {
        id "side_road"
        name "Side Street"
        translation 0 0 0
        startJunction "junction_002"
        endJunction "junction_003"
        width 7.0
        numberOfLanes 2
        wayPoints [
            20 0 0
            20 15 0
        ]
    }'''
    
    # 道路3: 重复ID测试
    road3_wbt = '''StraightRoadSegment {
        id "main_road"
        name "Another Main"
        translation 0 20 0
        startJunction "junction_004"
        endJunction "junction_005"
        width 7.0
        length 15.0
    }'''
    
    # 创建Road对象
    road1 = Road(road1_wbt, "Road")
    road2 = Road(road2_wbt, "Road")
    road3 = Road(road3_wbt, "StraightRoadSegment")
    
    Road.roads = [road1, road2, road3]
    
    # 创建十字路口
    # junction_001: 正常位置
    crossroad1_wbt = '''Crossroad {
        id "junction_001"
        name "Start Junction"
        translation 0 0 0
        connectedRoadIDs ["main_road"]
    }'''
    
    # junction_002: 正确位置 (20, 0, 0)
    crossroad2_wbt = '''Crossroad {
        id "junction_002"
        name "Center Junction"
        translation 20 0 0
        connectedRoadIDs ["main_road", "side_road"]
    }'''
    
    # junction_002: 错误位置 - 相同ID但位置不同！
    crossroad3_wbt = '''RoadIntersection {
        id "junction_002"
        name "Wrong Center"
        translation 20 5 0
        roadNumber 4
    }'''
    
    # junction_003: 正常
    crossroad4_wbt = '''Crossroad {
        id "junction_003"
        name "End Junction"
        translation 20 15 0
        connectedRoadIDs ["side_road"]
    }'''
    
    # 重复ID的十字路口
    crossroad5_wbt = '''Crossroad {
        id "junction_001"
        name "Duplicate Start"
        translation 0 0 0
        connectedRoadIDs []
    }'''
    
    # 创建Crossroad对象
    crossroad1 = Crossroad("Crossroad")
    crossroad1.init_from_wbt_string(crossroad1_wbt)
    
    crossroad2 = Crossroad("Crossroad")
    crossroad2.init_from_wbt_string(crossroad2_wbt)
    
    crossroad3 = Crossroad("RoadIntersection")
    crossroad3.init_from_wbt_string(crossroad3_wbt)
    
    crossroad4 = Crossroad("Crossroad")
    crossroad4.init_from_wbt_string(crossroad4_wbt)
    
    crossroad5 = Crossroad("Crossroad")
    crossroad5.init_from_wbt_string(crossroad5_wbt)
    
    Crossroad.crossroads = [crossroad1, crossroad2, crossroad3, crossroad4, crossroad5]
    
    print(f"创建了 {len(Road.roads)} 个道路和 {len(Crossroad.crossroads)} 个十字路口")


def test_road_checker():
    """测试RoadChecker功能"""
    print("="*60)
    print("测试RoadChecker功能")
    print("="*60)
    
    # 创建测试数据
    create_test_data_with_position_inconsistency()
    
    # 执行检查
    checker = RoadChecker()
    result = checker.check_map_data()
    
    print("\n" + "="*60)
    print("预期结果分析:")
    print("="*60)
    print("应该发现以下问题:")
    print("1. 重复的道路ID: 'main_road'")
    print("2. 重复的十字路口ID: 'junction_001' 和 'junction_002'")
    print("3. Junction位置不一致: 'junction_002' 在两个不同位置")
    print("   - 参考位置: (20, 0, 0) 来自道路连接")
    print("   - 错误位置: (20, 5, 0) 来自RoadIntersection定义")
    print("   - 偏差: 5.0m")
    print("4. 涉及的道路:")
    print("   - Main Street (main_road) - end_junction")
    print("   - Side Street (side_road) - start_junction")
    
    return result


def main():
    """主函数"""
    try:
        result = test_road_checker()
        
        print(f"\n测试完成!")
        print(f"发现 {result['total_errors']} 个错误和 {result['total_warnings']} 个警告")
        
        if result['total_errors'] > 0:
            print("✅ 测试成功 - 检测到预期的错误")
        else:
            print("❌ 测试可能有问题 - 没有检测到预期的错误")
            
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
