"""Road data checker for map validation."""

import math
from collections import defaultdict
from typing import List, Dict, Tuple, Set
from road import Road
from crossroad import Crossroad
from node_extractor import NodeExtractor


class RoadChecker:
    """检查地图数据的完整性和一致性"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        
    def check_map_data(self, wbt_file: str = None, roads: List[Road] = None, crossroads: List[Crossroad] = None) -> Dict:
        """
        检查地图数据
        
        Args:
            wbt_file: Webots文件路径，如果提供则从文件加载数据
            roads: Road对象列表，如果不提供则使用Road.roads
            crossroads: Crossroad对象列表，如果不提供则使用Crossroad.crossroads
            
        Returns:
            dict: 检查结果，包含errors和warnings
        """
        self.errors = []
        self.warnings = []
        
        # 如果提供了wbt文件，则从文件加载数据
        if wbt_file:
            self._load_data_from_file(wbt_file)
            roads = Road.roads
            crossroads = Crossroad.crossroads
        else:
            # 使用提供的数据或默认的类变量
            roads = roads if roads is not None else Road.roads
            crossroads = crossroads if crossroads is not None else Crossroad.crossroads
        
        print(f"开始检查地图数据...")
        print(f"  - 道路数量: {len(roads)}")
        print(f"  - 十字路口数量: {len(crossroads)}")
        
        # 执行各项检查
        self._check_duplicate_ids_and_names(roads, crossroads)
        self._check_junction_position_consistency(roads, crossroads)
        
        # 返回检查结果
        result = {
            'errors': self.errors,
            'warnings': self.warnings,
            'total_errors': len(self.errors),
            'total_warnings': len(self.warnings),
            'roads_count': len(roads),
            'crossroads_count': len(crossroads)
        }
        
        self._print_summary(result)
        return result
    
    def _load_data_from_file(self, wbt_file: str):
        """从Webots文件加载道路和十字路口数据"""
        print(f"从文件加载数据: {wbt_file}")
        
        # 清空现有数据
        Road.roads = []
        Crossroad.crossroads = []
        
        # 提取道路信息
        node_extractor = NodeExtractor(wbt_file)
        road_types = ["Road", "StraightRoadSegment", "CurvedRoadSegment"]
        
        for road_type in road_types:
            for road_string in node_extractor.extractRootNodes(road_type):
                road = Road(road_string, road_type)
                Road.roads.append(road)
        
        # 提取十字路口信息
        crossroad_types = ["Crossroad", "RoadIntersection", "LaneSeparation"]
        for crossroad_type in crossroad_types:
            for crossroad_string in node_extractor.extractRootNodes(crossroad_type):
                crossroad = Crossroad(crossroad_type)
                crossroad.init_from_wbt_string(crossroad_string)
                Crossroad.crossroads.append(crossroad)
    
    def _check_duplicate_ids_and_names(self, roads: List[Road], crossroads: List[Crossroad]):
        """检查road和crossroad的id和name是否存在重复"""
        print("检查ID和名称重复...")
        
        # 收集所有ID和名称
        road_ids = defaultdict(list)
        road_names = defaultdict(list)
        crossroad_ids = defaultdict(list)
        crossroad_names = defaultdict(list)
        
        # 收集道路ID和名称
        for road in roads:
            if road.id:
                road_ids[road.id].append(road)
            if road.name:
                road_names[road.name].append(road)
        
        # 收集十字路口ID和名称
        for crossroad in crossroads:
            if crossroad.id:
                crossroad_ids[crossroad.id].append(crossroad)
            if hasattr(crossroad, 'name') and crossroad.name:
                crossroad_names[crossroad.name].append(crossroad)
        
        # 检查道路ID重复
        for road_id, road_list in road_ids.items():
            if len(road_list) > 1:
                self.errors.append({
                    'type': 'duplicate_road_id',
                    'message': f"道路ID重复: '{road_id}' 出现在 {len(road_list)} 个道路中",
                    'details': {
                        'id': road_id,
                        'roads': [{'type': r.roadType, 'name': r.name} for r in road_list]
                    }
                })
        
        # 检查道路名称重复
        for road_name, road_list in road_names.items():
            if len(road_list) > 1:
                self.warnings.append({
                    'type': 'duplicate_road_name',
                    'message': f"道路名称重复: '{road_name}' 出现在 {len(road_list)} 个道路中",
                    'details': {
                        'name': road_name,
                        'roads': [{'id': r.id, 'type': r.roadType} for r in road_list]
                    }
                })
        
        # 检查十字路口ID重复
        for crossroad_id, crossroad_list in crossroad_ids.items():
            if len(crossroad_list) > 1:
                self.errors.append({
                    'type': 'duplicate_crossroad_id',
                    'message': f"十字路口ID重复: '{crossroad_id}' 出现在 {len(crossroad_list)} 个十字路口中",
                    'details': {
                        'id': crossroad_id,
                        'crossroads': [{'type': c.crossroadType, 'name': getattr(c, 'name', '')} for c in crossroad_list]
                    }
                })
        
        # 检查十字路口名称重复
        for crossroad_name, crossroad_list in crossroad_names.items():
            if len(crossroad_list) > 1:
                self.warnings.append({
                    'type': 'duplicate_crossroad_name',
                    'message': f"十字路口名称重复: '{crossroad_name}' 出现在 {len(crossroad_list)} 个十字路口中",
                    'details': {
                        'name': crossroad_name,
                        'crossroads': [{'id': c.id, 'type': c.crossroadType} for c in crossroad_list]
                    }
                })
        
        # 检查道路和十字路口之间的ID冲突
        all_road_ids = set(road_ids.keys())
        all_crossroad_ids = set(crossroad_ids.keys())
        conflicting_ids = all_road_ids.intersection(all_crossroad_ids)
        
        for conflicting_id in conflicting_ids:
            self.errors.append({
                'type': 'road_crossroad_id_conflict',
                'message': f"道路和十字路口ID冲突: '{conflicting_id}' 同时用于道路和十字路口",
                'details': {
                    'id': conflicting_id,
                    'roads': [{'type': r.roadType, 'name': r.name} for r in road_ids[conflicting_id]],
                    'crossroads': [{'type': c.crossroadType, 'name': getattr(c, 'name', '')} for c in crossroad_ids[conflicting_id]]
                }
            })
    
    def _check_junction_position_consistency(self, roads: List[Road], crossroads: List[Crossroad]):
        """检查junction id一致但位置不在同一个地方的情况"""
        print("检查Junction位置一致性...")
        
        # 创建junction ID到位置的映射
        junction_positions = defaultdict(list)
        
        # 收集所有junction的位置信息
        for crossroad in crossroads:
            if crossroad.id and hasattr(crossroad, 'translation'):
                junction_positions[crossroad.id].append({
                    'position': crossroad.translation,
                    'source': 'crossroad',
                    'object': crossroad
                })
        
        # 从道路的起点和终点收集junction位置信息
        for road in roads:
            if road.startJunctionID and len(road.wayPoints) > 0:
                # 计算道路起点的实际位置（wayPoint + translation）
                start_pos = [
                    road.wayPoints[0][0] + road.translation[0],
                    road.wayPoints[0][1] + road.translation[1],
                    road.wayPoints[0][2] + road.translation[2]
                ]
                junction_positions[road.startJunctionID].append({
                    'position': start_pos,
                    'source': 'road_start',
                    'object': road
                })
            
            if road.endJunctionID and len(road.wayPoints) > 0:
                # 计算道路终点的实际位置
                end_pos = [
                    road.wayPoints[-1][0] + road.translation[0],
                    road.wayPoints[-1][1] + road.translation[1],
                    road.wayPoints[-1][2] + road.translation[2]
                ]
                junction_positions[road.endJunctionID].append({
                    'position': end_pos,
                    'source': 'road_end',
                    'object': road
                })
        
        # 检查每个junction ID的位置一致性
        for junction_id, position_list in junction_positions.items():
            if len(position_list) > 1:
                self._check_position_consistency_for_junction(junction_id, position_list)
    
    def _check_position_consistency_for_junction(self, junction_id: str, position_list: List[Dict]):
        """检查单个junction的位置一致性"""
        tolerance = 0.1  # 位置容差，单位：米

        # 获取第一个位置作为参考
        reference_pos = position_list[0]['position']
        reference_info = position_list[0]

        inconsistent_positions = []
        max_distance = 0.0

        for i, pos_info in enumerate(position_list[1:], 1):
            current_pos = pos_info['position']
            distance = self._calculate_distance(reference_pos, current_pos)
            max_distance = max(max_distance, distance)

            if distance > tolerance:
                # 获取详细的road信息
                road_info = self._get_detailed_road_info(pos_info['object'], pos_info['source'])

                inconsistent_positions.append({
                    'position': current_pos,
                    'distance': distance,
                    'source': pos_info['source'],
                    'object_info': self._get_object_info(pos_info['object']),
                    'road_info': road_info
                })

        if inconsistent_positions:
            # 获取参考位置的详细road信息
            reference_road_info = self._get_detailed_road_info(reference_info['object'], reference_info['source'])

            # 构建详细的错误消息
            error_message = f"Junction '{junction_id}' 的位置不一致，最大偏差: {max_distance:.3f}m"

            self.errors.append({
                'type': 'junction_position_inconsistency',
                'message': error_message,
                'details': {
                    'junction_id': junction_id,
                    'max_deviation': max_distance,
                    'reference_position': reference_pos,
                    'reference_source': reference_info['source'],
                    'reference_object': self._get_object_info(reference_info['object']),
                    'reference_road_info': reference_road_info,
                    'inconsistent_positions': inconsistent_positions,
                    'tolerance': tolerance
                }
            })
    
    def _calculate_distance(self, pos1: List[float], pos2: List[float]) -> float:
        """计算两个3D位置之间的欧几里得距离"""
        return math.sqrt(
            (pos1[0] - pos2[0]) ** 2 +
            (pos1[1] - pos2[1]) ** 2 +
            (pos1[2] - pos2[2]) ** 2
        )
    
    def _get_object_info(self, obj) -> Dict:
        """获取对象的基本信息"""
        if hasattr(obj, 'roadType'):  # Road对象
            return {
                'type': 'road',
                'id': obj.id,
                'name': obj.name,
                'road_type': obj.roadType
            }
        elif hasattr(obj, 'crossroadType'):  # Crossroad对象
            return {
                'type': 'crossroad',
                'id': obj.id,
                'name': getattr(obj, 'name', ''),
                'crossroad_type': obj.crossroadType
            }
        else:
            return {'type': 'unknown'}

    def _get_detailed_road_info(self, obj, source: str) -> Dict:
        """获取详细的road信息，包括junction关联"""
        if hasattr(obj, 'roadType'):  # Road对象
            road_info = {
                'road_id': obj.id,
                'road_name': obj.name,
                'road_type': obj.roadType,
                'junction_role': 'unknown'
            }

            if source == 'road_start':
                road_info['junction_role'] = 'start_junction'
                road_info['junction_id'] = getattr(obj, 'startJunctionID', '')
            elif source == 'road_end':
                road_info['junction_role'] = 'end_junction'
                road_info['junction_id'] = getattr(obj, 'endJunctionID', '')

            return road_info
        elif hasattr(obj, 'crossroadType'):  # Crossroad对象
            return {
                'road_id': 'N/A',
                'road_name': 'N/A',
                'road_type': 'N/A',
                'junction_role': 'crossroad_definition',
                'junction_id': obj.id,
                'crossroad_type': obj.crossroadType
            }
        else:
            return {
                'road_id': 'unknown',
                'road_name': 'unknown',
                'road_type': 'unknown',
                'junction_role': 'unknown'
            }
    
    def _print_summary(self, result: Dict):
        """打印检查结果摘要"""
        print("\n" + "="*50)
        print("地图数据检查结果摘要")
        print("="*50)
        print(f"道路数量: {result['roads_count']}")
        print(f"十字路口数量: {result['crossroads_count']}")
        print(f"错误数量: {result['total_errors']}")
        print(f"警告数量: {result['total_warnings']}")

        if result['errors']:
            print("\n错误详情:")
            for i, error in enumerate(result['errors'], 1):
                print(f"  {i}. {error['message']}")

                # 对于junction位置不一致错误，显示详细信息
                if error['type'] == 'junction_position_inconsistency':
                    details = error['details']
                    print(f"      Junction ID: {details['junction_id']}")
                    print(f"      最大偏差: {details['max_deviation']:.3f}m (容差: {details['tolerance']}m)")

                    # 显示参考位置信息
                    ref_road = details['reference_road_info']
                    print(f"      参考位置:")
                    print(f"        - 坐标: ({details['reference_position'][0]:.3f}, {details['reference_position'][1]:.3f}, {details['reference_position'][2]:.3f})")
                    print(f"        - 来源: {details['reference_source']}")
                    if ref_road['road_name'] != 'N/A':
                        print(f"        - 道路: {ref_road['road_name']} (ID: {ref_road['road_id']}, 类型: {ref_road['road_type']})")
                        print(f"        - Junction角色: {ref_road['junction_role']}")

                    # 显示不一致位置信息
                    print(f"      不一致位置 ({len(details['inconsistent_positions'])}个):")
                    for j, pos_info in enumerate(details['inconsistent_positions'], 1):
                        road_info = pos_info['road_info']
                        print(f"        {j}. 偏差: {pos_info['distance']:.3f}m")
                        print(f"           坐标: ({pos_info['position'][0]:.3f}, {pos_info['position'][1]:.3f}, {pos_info['position'][2]:.3f})")
                        print(f"           来源: {pos_info['source']}")
                        if road_info['road_name'] != 'N/A':
                            print(f"           道路: {road_info['road_name']} (ID: {road_info['road_id']}, 类型: {road_info['road_type']})")
                            print(f"           Junction角色: {road_info['junction_role']}")
                        print()

        if result['warnings']:
            print("\n警告详情:")
            for i, warning in enumerate(result['warnings'], 1):
                print(f"  {i}. {warning['message']}")

        if not result['errors'] and not result['warnings']:
            print("\n✅ 未发现任何问题！")

        print("="*50)


def main():
    """主函数，用于命令行调用"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python road_checker.py <webots_file.wbt>")
        sys.exit(1)
    
    wbt_file = sys.argv[1]
    checker = RoadChecker()
    result = checker.check_map_data(wbt_file)
    
    # 如果有错误，返回非零退出码
    if result['total_errors'] > 0:
        sys.exit(1)


if __name__ == "__main__":
    main()
